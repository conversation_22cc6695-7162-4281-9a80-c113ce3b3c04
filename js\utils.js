// ADAMS Utility Functions
class ADAMSUtils {
    // Date and Time Utilities
    static formatDate(date) {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }).format(new Date(date));
    }
    
    static formatDateTime(date) {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }).format(new Date(date));
    }
    
    static timeAgo(date) {
        const now = new Date();
        const past = new Date(date);
        const diffInSeconds = Math.floor((now - past) / 1000);
        
        if (diffInSeconds < 60) return 'just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} days ago`;
        return this.formatDate(date);
    }
    
    // File Utilities
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    static getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const icons = {
            pdf: 'fa-file-pdf',
            doc: 'fa-file-word',
            docx: 'fa-file-word',
            xls: 'fa-file-excel',
            xlsx: 'fa-file-excel',
            ppt: 'fa-file-powerpoint',
            pptx: 'fa-file-powerpoint',
            txt: 'fa-file-alt',
            jpg: 'fa-file-image',
            jpeg: 'fa-file-image',
            png: 'fa-file-image',
            gif: 'fa-file-image',
            zip: 'fa-file-archive',
            rar: 'fa-file-archive'
        };
        return icons[ext] || 'fa-file';
    }
    
    // Status Utilities
    static getStatusBadge(status) {
        const statusConfig = {
            approved: { class: 'bg-success', icon: 'fa-check-circle' },
            pending: { class: 'bg-warning', icon: 'fa-clock' },
            rejected: { class: 'bg-danger', icon: 'fa-times-circle' },
            draft: { class: 'bg-info', icon: 'fa-edit' },
            submitted: { class: 'bg-primary', icon: 'fa-paper-plane' },
            reviewed: { class: 'bg-secondary', icon: 'fa-eye' }
        };
        
        const config = statusConfig[status.toLowerCase()] || statusConfig.pending;
        return `<span class="badge ${config.class}"><i class="fas ${config.icon} me-1"></i>${status}</span>`;
    }
    
    static getStatusColor(status) {
        const colors = {
            approved: 'success',
            pending: 'warning',
            rejected: 'danger',
            draft: 'info',
            submitted: 'primary',
            reviewed: 'secondary'
        };
        return colors[status.toLowerCase()] || 'warning';
    }
    
    // Validation Utilities
    static validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
    static validatePassword(password) {
        // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
        const re = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
        return re.test(password);
    }
    
    static validateFileType(file, allowedTypes) {
        const fileType = file.type;
        return allowedTypes.includes(fileType);
    }
    
    static validateFileSize(file, maxSize) {
        return file.size <= maxSize;
    }
    
    // Search and Filter Utilities
    static searchArray(array, searchTerm, fields = []) {
        if (!searchTerm) return array;
        
        return array.filter(item => {
            if (fields.length > 0) {
                return fields.some(field => {
                    const value = this.getNestedValue(item, field);
                    return value && value.toString().toLowerCase().includes(searchTerm.toLowerCase());
                });
            } else {
                return Object.values(item).some(value => 
                    value && value.toString().toLowerCase().includes(searchTerm.toLowerCase())
                );
            }
        });
    }
    
    static getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => current && current[key], obj);
    }
    
    static filterArray(array, filters) {
        return array.filter(item => {
            return Object.keys(filters).every(key => {
                const filterValue = filters[key];
                const itemValue = this.getNestedValue(item, key);
                
                if (filterValue === null || filterValue === undefined || filterValue === '') {
                    return true;
                }
                
                if (Array.isArray(filterValue)) {
                    return filterValue.includes(itemValue);
                }
                
                return itemValue === filterValue;
            });
        });
    }
    
    // Pagination Utilities
    static paginateArray(array, page, perPage) {
        const startIndex = (page - 1) * perPage;
        const endIndex = startIndex + perPage;
        
        return {
            data: array.slice(startIndex, endIndex),
            pagination: {
                current: page,
                perPage: perPage,
                total: array.length,
                totalPages: Math.ceil(array.length / perPage),
                hasNext: endIndex < array.length,
                hasPrev: page > 1
            }
        };
    }
    
    // UI Utilities
    static showLoading(element) {
        if (element) {
            element.innerHTML = '<div class="text-center py-5"><div class="spinner"></div><p class="mt-3">Loading...</p></div>';
        }
    }
    
    static hideLoading(element) {
        if (element) {
            element.innerHTML = '';
        }
    }
    
    static showToast(message, type = 'info', duration = 5000) {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, duration);
    }
    
    static confirmAction(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }
    
    // Data Export Utilities
    static exportToCSV(data, filename) {
        if (!data || data.length === 0) return;
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
        ].join('\n');
        
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename || 'export.csv';
        link.click();
    }
    
    static exportToJSON(data, filename) {
        const jsonContent = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = filename || 'export.json';
        link.click();
    }
    
    // Chart Data Utilities
    static generateChartData(data, xField, yField, type = 'line') {
        return {
            labels: data.map(item => item[xField]),
            datasets: [{
                label: yField,
                data: data.map(item => item[yField]),
                borderColor: '#2E7D32',
                backgroundColor: 'rgba(46, 125, 50, 0.1)',
                tension: 0.4
            }]
        };
    }
    
    // Mock Data Generators
    static generateMockUsers(count = 10) {
        const roles = ['admin', 'areachair', 'subareachair', 'member'];
        const departments = ['Computer Science', 'Business Administration', 'Education', 'Engineering'];
        
        return Array.from({ length: count }, (_, i) => ({
            id: i + 1,
            username: `user${i + 1}`,
            name: `User ${i + 1}`,
            email: `user${i + 1}@ndmc.edu.ph`,
            role: roles[Math.floor(Math.random() * roles.length)],
            department: departments[Math.floor(Math.random() * departments.length)],
            status: Math.random() > 0.2 ? 'active' : 'inactive',
            createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000)
        }));
    }
    
    static generateMockDocuments(count = 20) {
        const types = ['Curriculum', 'Faculty Credentials', 'Facilities', 'Research', 'Student Records'];
        const statuses = ['draft', 'submitted', 'pending', 'approved', 'rejected'];
        
        return Array.from({ length: count }, (_, i) => ({
            id: i + 1,
            title: `Document ${i + 1}`,
            type: types[Math.floor(Math.random() * types.length)],
            status: statuses[Math.floor(Math.random() * statuses.length)],
            submittedBy: `User ${Math.floor(Math.random() * 10) + 1}`,
            submittedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
            fileSize: Math.floor(Math.random() * 5000000) + 100000,
            fileName: `document_${i + 1}.pdf`
        }));
    }
    
    // Local Storage Utilities
    static setLocalStorage(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (e) {
            console.error('Error saving to localStorage:', e);
        }
    }
    
    static getLocalStorage(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (e) {
            console.error('Error reading from localStorage:', e);
            return defaultValue;
        }
    }
    
    static removeLocalStorage(key) {
        try {
            localStorage.removeItem(key);
        } catch (e) {
            console.error('Error removing from localStorage:', e);
        }
    }
    
    // Network Utilities
    static async fetchWithTimeout(url, options = {}, timeout = 5000) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }
    
    // Form Utilities
    static serializeForm(form) {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            if (data[key]) {
                if (!Array.isArray(data[key])) {
                    data[key] = [data[key]];
                }
                data[key].push(value);
            } else {
                data[key] = value;
            }
        }
        
        return data;
    }
    
    static resetForm(form) {
        form.reset();
        form.querySelectorAll('.is-invalid').forEach(field => {
            field.classList.remove('is-invalid');
        });
    }
}

// Global utility functions
window.ADAMSUtils = ADAMSUtils; 