<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Area Chair Dashboard - Accreditation Data Administration & Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../css/styles.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="sidebar-brand">Accreditation Data Administration & Management System</div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="review">
                        <i class="fas fa-eye nav-icon"></i>
                        <span class="nav-text">Document Review</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="progress">
                        <i class="fas fa-chart-line nav-icon"></i>
                        <span class="nav-text">Progress Tracking</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="documents">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">Document Access</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="reports">
                        <i class="fas fa-chart-bar nav-icon"></i>
                        <span class="nav-text">Reports</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="notifications">
                        <i class="fas fa-bell nav-icon"></i>
                        <span class="nav-text">Notifications</span>
                    </a>
                </div>
                
                <hr class="my-3 mx-3" style="border-color: rgba(255,255,255,0.2);">
                
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="logout()">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <div class="d-flex align-items-center">
                    <button class="sidebar-toggle me-3" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="sidebar-toggle d-lg-none" id="mobileSidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title mb-0" id="pageTitle">Dashboard</h1>
                </div>
                
                <div class="top-nav-actions">
                    <div class="notification-badge me-3">
                        <i class="fas fa-bell fa-lg"></i>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>
                            <span id="topUserName">areachair</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="content-area" id="contentArea">
                <!-- Dashboard content will be loaded here -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- <script src="../js/auth.js"></script> -->
    <!-- <script src="../js/utils.js"></script> -->
    <!-- <script src="../js/dashboard.js"></script> -->
    <script>
        // Area Chair-specific dashboard functionality
        class AreaChairDashboard extends ADAMSDashboard {
            constructor() {
                super();
                this.setupAreaChairFeatures();
            }
            
            setupAreaChairFeatures() {
                this.loadUserData();
            }
            
            loadUserData() {
                const user = ADAMSAuth.getCurrentUser();
                if (user) {
                    document.getElementById('userName').textContent = user.username;
                    document.getElementById('topUserName').textContent = user.username;
                }
            }
            
            renderDocumentReview(container) {
                const documents = [
                    {
                        id: 1,
                        title: 'BSIT Curriculum 2024',
                        type: 'Curriculum',
                        submittedBy: 'Dr. Maria Santos',
                        submittedAt: new Date(Date.now() - 86400000),
                        status: 'pending',
                        priority: 'high',
                        fileSize: 2048576,
                        fileName: 'bsit_curriculum_2024.pdf'
                    },
                    {
                        id: 2,
                        title: 'Faculty Credentials - Computer Science',
                        type: 'Faculty Credentials',
                        submittedBy: 'Prof. Juan Dela Cruz',
                        submittedAt: new Date(Date.now() - 172800000),
                        status: 'pending',
                        priority: 'medium',
                        fileSize: 1536000,
                        fileName: 'faculty_credentials_cs.pdf'
                    },
                    {
                        id: 3,
                        title: 'Laboratory Facilities Report',
                        type: 'Facilities',
                        submittedBy: 'Engr. Ana Reyes',
                        submittedAt: new Date(Date.now() - 259200000),
                        status: 'approved',
                        priority: 'low',
                        fileSize: 3072000,
                        fileName: 'lab_facilities_report.pdf'
                    }
                ];
                
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Document Review</h2>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" placeholder="Search documents..." id="docSearch">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="approved">Approved</option>
                                    <option value="rejected">Rejected</option>
                                </select>
                                <select class="form-select" id="priorityFilter">
                                    <option value="">All Priority</option>
                                    <option value="high">High</option>
                                    <option value="medium">Medium</option>
                                    <option value="low">Low</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Documents Pending Review</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Document</th>
                                            <th>Type</th>
                                            <th>Submitted By</th>
                                            <th>Submitted</th>
                                            <th>Priority</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${documents.map(doc => `
                                            <tr>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas ${ADAMSUtils.getFileIcon(doc.fileName)} fa-2x text-muted me-3"></i>
                                                        <div>
                                                            <div class="fw-semibold">${doc.title}</div>
                                                            <small class="text-muted">${ADAMSUtils.formatFileSize(doc.fileSize)}</small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>${doc.type}</td>
                                                <td>${doc.submittedBy}</td>
                                                <td>${ADAMSUtils.timeAgo(doc.submittedAt)}</td>
                                                <td>
                                                    <span class="badge bg-${this.getPriorityColor(doc.priority)}">
                                                        ${doc.priority}
                                                    </span>
                                                </td>
                                                <td>${ADAMSUtils.getStatusBadge(doc.status)}</td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-primary btn-view" title="View Document">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-success btn-approve" title="Approve" ${doc.status === 'approved' ? 'disabled' : ''}>
                                                            <i class="fas fa-check"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger btn-reject" title="Reject" ${doc.status === 'rejected' ? 'disabled' : ''}>
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                        <button class="btn btn-outline-info btn-comment" title="Add Comment">
                                                            <i class="fas fa-comment"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
                
                // Add event listeners for review actions
                this.setupReviewEventListeners();
            }
            
            renderProgressTracking(container) {
                const areas = [
                    { name: 'Area I - Vision, Mission, Goals and Objectives', progress: 85, status: 'in-progress' },
                    { name: 'Area II - Faculty', progress: 92, status: 'completed' },
                    { name: 'Area III - Curriculum and Instruction', progress: 78, status: 'in-progress' },
                    { name: 'Area IV - Support to Students', progress: 65, status: 'in-progress' },
                    { name: 'Area V - Research', progress: 45, status: 'pending' },
                    { name: 'Area VI - Extension and Community Involvement', progress: 70, status: 'in-progress' },
                    { name: 'Area VII - Physical Plant and Facilities', progress: 88, status: 'completed' },
                    { name: 'Area VIII - Laboratories', progress: 82, status: 'in-progress' },
                    { name: 'Area IX - Library', progress: 95, status: 'completed' },
                    { name: 'Area X - Administration', progress: 90, status: 'completed' }
                ];
                
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Progress Tracking</h2>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <div class="metric-value">78%</div>
                                <div class="metric-label">Overall Progress</div>
                                <i class="fas fa-chart-pie metric-icon"></i>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <div class="metric-value">4</div>
                                <div class="metric-label">Completed Areas</div>
                                <i class="fas fa-check-circle metric-icon"></i>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <div class="metric-value">5</div>
                                <div class="metric-label">In Progress</div>
                                <i class="fas fa-clock metric-icon"></i>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 mb-3">
                            <div class="metric-card">
                                <div class="metric-value">1</div>
                                <div class="metric-label">Pending Areas</div>
                                <i class="fas fa-exclamation-triangle metric-icon"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Accreditation Areas Progress</h5>
                        </div>
                        <div class="card-body">
                            ${areas.map(area => `
                                <div class="area-progress mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0">${area.name}</h6>
                                        <span class="badge bg-${this.getStatusColor(area.status)}">${area.progress}%</span>
                                    </div>
                                    <div class="progress">
                                        <div class="progress-bar bg-${this.getProgressColor(area.progress)}" 
                                             style="width: ${area.progress}%" 
                                             role="progressbar" 
                                             aria-valuenow="${area.progress}" 
                                             aria-valuemin="0" 
                                             aria-valuemax="100">
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between mt-2">
                                        <small class="text-muted">Status: ${area.status}</small>
                                        <small class="text-muted">${area.progress}% Complete</small>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
            
            renderReports(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Area Reports</h2>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Document Status Summary</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="documentStatusChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Review Activity</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="reviewActivityChart" width="400" height="200"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Report Generation</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4 mb-3">
                                            <div class="card border">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-file-pdf fa-3x text-danger mb-3"></i>
                                                    <h6>Area Progress Report</h6>
                                                    <p class="text-muted">Generate area-specific progress report</p>
                                                    <button class="btn btn-outline-danger">Generate PDF</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card border">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-file-excel fa-3x text-success mb-3"></i>
                                                    <h6>Review Summary</h6>
                                                    <p class="text-muted">Export review activities to Excel</p>
                                                    <button class="btn btn-outline-success">Export Excel</button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <div class="card border">
                                                <div class="card-body text-center">
                                                    <i class="fas fa-chart-bar fa-3x text-primary mb-3"></i>
                                                    <h6>Analytics Report</h6>
                                                    <p class="text-muted">Generate detailed analytics</p>
                                                    <button class="btn btn-outline-primary">Generate Report</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderNotifications(container) {
                const notifications = [
                    {
                        id: 1,
                        title: 'New Document Submitted',
                        message: 'BSIT Curriculum 2024 has been submitted for review',
                        time: '5 minutes ago',
                        type: 'info',
                        read: false
                    },
                    {
                        id: 2,
                        title: 'Review Deadline Reminder',
                        message: 'Faculty credentials review due in 2 days',
                        time: '1 hour ago',
                        type: 'warning',
                        read: false
                    },
                    {
                        id: 3,
                        title: 'Document Approved',
                        message: 'Laboratory Facilities Report has been approved',
                        time: '2 hours ago',
                        type: 'success',
                        read: true
                    },
                    {
                        id: 4,
                        title: 'System Update',
                        message: 'New features available in document review',
                        time: '1 day ago',
                        type: 'info',
                        read: true
                    }
                ];
                
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Notifications</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Recent Notifications</h5>
                                <button class="btn btn-outline-secondary btn-sm">Mark All Read</button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="notification-list">
                                ${notifications.map(notification => `
                                    <div class="notification-item d-flex align-items-start p-3 border-bottom ${!notification.read ? 'bg-light' : ''}">
                                        <div class="notification-icon me-3">
                                            <i class="fas fa-${this.getNotificationIcon(notification.type)} text-${notification.type}"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="mb-1 ${!notification.read ? 'fw-bold' : ''}">${notification.title}</h6>
                                                    <p class="text-muted mb-1">${notification.message}</p>
                                                    <small class="text-muted">${notification.time}</small>
                                                </div>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-sm">View</button>
                                                    <button class="btn btn-outline-secondary btn-sm">Mark Read</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            setupReviewEventListeners() {
                // Add event listeners for review actions
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.btn-approve')) {
                        this.handleApprove(e);
                    }
                    if (e.target.closest('.btn-reject')) {
                        this.handleReject(e);
                    }
                    if (e.target.closest('.btn-comment')) {
                        this.handleComment(e);
                    }
                });
            }
            
            handleApprove(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to approve this document?')) {
                    ADAMSUtils.showToast('Document approved successfully', 'success');
                    // Update UI to reflect approval
                    const row = e.target.closest('tr');
                    const statusCell = row.querySelector('td:nth-child(6)');
                    statusCell.innerHTML = ADAMSUtils.getStatusBadge('approved');
                    
                    // Disable approve button, enable reject button
                    const approveBtn = e.target.closest('.btn-approve');
                    const rejectBtn = row.querySelector('.btn-reject');
                    approveBtn.disabled = true;
                    rejectBtn.disabled = false;
                }
            }
            
            handleReject(e) {
                e.preventDefault();
                const reason = prompt('Please provide a reason for rejection:');
                if (reason) {
                    ADAMSUtils.showToast('Document rejected', 'warning');
                    // Update UI to reflect rejection
                    const row = e.target.closest('tr');
                    const statusCell = row.querySelector('td:nth-child(6)');
                    statusCell.innerHTML = ADAMSUtils.getStatusBadge('rejected');
                    
                    // Disable reject button, enable approve button
                    const rejectBtn = e.target.closest('.btn-reject');
                    const approveBtn = row.querySelector('.btn-approve');
                    rejectBtn.disabled = true;
                    approveBtn.disabled = false;
                }
            }
            
            handleComment(e) {
                e.preventDefault();
                const comment = prompt('Add your comment:');
                if (comment) {
                    ADAMSUtils.showToast('Comment added successfully', 'info');
                }
            }
            
            getPriorityColor(priority) {
                const colors = {
                    high: 'danger',
                    medium: 'warning',
                    low: 'info'
                };
                return colors[priority] || 'secondary';
            }
            
            getStatusColor(status) {
                const colors = {
                    completed: 'success',
                    'in-progress': 'warning',
                    pending: 'info'
                };
                return colors[status] || 'secondary';
            }
            
            getProgressColor(progress) {
                if (progress >= 90) return 'success';
                if (progress >= 70) return 'warning';
                if (progress >= 50) return 'info';
                return 'danger';
            }
            
            getNotificationIcon(type) {
                const icons = {
                    info: 'info-circle',
                    warning: 'exclamation-triangle',
                    success: 'check-circle',
                    danger: 'times-circle'
                };
                return icons[type] || 'bell';
            }
        }
        
        // Initialize area chair dashboard
        document.addEventListener('DOMContentLoaded', () => {
            new AreaChairDashboard();
        });
    </script>
</body>
</html> 