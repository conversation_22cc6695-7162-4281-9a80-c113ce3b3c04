# Accreditation Management System

A comprehensive web-based system for managing accreditation processes, document workflows, and institutional compliance tracking.

## Features

- **Role-Based Access Control**: Four distinct user roles (<PERSON><PERSON>, Area Chair, Sub-Area Chair, Member)
- **Document Management**: Hierarchical organization with workflow tracking
- **Real-Time Dashboards**: Progress tracking and analytics
- **Review & Approval Workflow**: Multi-level approval process with comments
- **Notification System**: In-app and email notifications
- **Comprehensive Reporting**: Exportable reports and analytics
- **Audit Trail**: Complete activity logging and compliance tracking

## Project Structure

```
accreditation-system/
├── index.html                 # Main entry point
├── login.html                 # Authentication page
├── assets/
│   ├── css/
│   │   ├── main.css          # Main stylesheet
│   │   ├── components.css    # Component library
│   │   ├── dashboard.css     # Dashboard-specific styles
│   │   └── responsive.css    # Mobile responsiveness
│   ├── js/
│   │   ├── app.js           # Main application controller
│   │   ├── auth.js          # Authentication management
│   │   ├── dashboard.js     # Dashboard functionality
│   │   ├── documents.js     # Document management
│   │   ├── notifications.js # Notification system
│   │   ├── reports.js       # Reporting functionality
│   │   └── utils.js         # Utility functions
│   ├── images/
│   └── data/
│       └── mock-data.js     # Sample data for testing
├── pages/
│   ├── dashboard.html       # Main dashboard
│   ├── documents.html       # Document management
│   ├── users.html          # User management (admin)
│   ├── reviews.html        # Review workflow
│   ├── reports.html        # Reports and analytics
│   └── profile.html        # User profile
└── components/
    ├── navigation.html     # Navigation component
    ├── modals.html        # Modal dialogs
    └── forms.html         # Form components
```

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework**: Bootstrap 5 for responsive design
- **Charts**: Chart.js for data visualization
- **Icons**: Bootstrap Icons
- **Storage**: Local Storage for user preferences and mock data

## User Roles & Permissions

### Admin
- Full system access
- User management (CRUD)
- System configuration
- All reports and analytics

### Area Chair
- Final approval authority for their area
- View all documents in their area
- Approve/reject submissions
- Area-specific reports

### Sub-Area Chair
- Review documents in their sub-area
- Recommend for approval
- Add review comments
- Sub-area progress tracking

### Member
- Submit documents
- View submission status
- Update own submissions
- Basic progress tracking

## Getting Started

1. Open `index.html` in a web browser
2. Use the login page to authenticate with different roles
3. Navigate through the system based on your role permissions

## Sample Login Credentials

- **Admin**: <EMAIL> / admin123
- **Area Chair**: <EMAIL> / chair123
- **Sub-Area Chair**: <EMAIL> / sub123
- **Member**: <EMAIL> / member123

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Development

The system uses vanilla JavaScript with a modular approach. Each major feature is contained in its own JavaScript module for maintainability.

### Key Components

- **Authentication**: Role-based access control with session management
- **Document Workflow**: Multi-stage approval process with status tracking
- **Real-Time Updates**: Dynamic content updates without page refresh
- **Responsive Design**: Mobile-first approach with progressive enhancement

## License

This project is for educational and demonstration purposes.
