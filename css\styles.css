/* ADAMS - Notre Dame of Midsayap College Theme - Enhanced */
:root {
    /* Enhanced Color Palette */
    --ndmc-green: #2E7D32;
    --ndmc-green-light: #4CAF50;
    --ndmc-green-dark: #1B5E20;
    --ndmc-green-lighter: #C8E6C9;
    --ndmc-green-darker: #0D4F14;
    --ndmc-green-gradient: linear-gradient(135deg, #2E7D32 0%, #4CAF50 50%, #1B5E20 100%);
    --ndmc-green-shadow: 0 4px 15px rgba(46, 125, 50, 0.3);
    
    /* Enhanced Status Colors */
    --success-light: #E8F5E8;
    --success-gradient: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
    --warning-light: #FFF8E1;
    --warning-gradient: linear-gradient(135deg, #FFC107 0%, #FFD54F 100%);
    --danger-light: #FFEBEE;
    --danger-gradient: linear-gradient(135deg, #F44336 0%, #EF5350 100%);
    --info-light: #E3F2FD;
    --info-gradient: linear-gradient(135deg, #2196F3 0%, #42A5F5 100%);
    
    /* Enhanced Gray Scale */
    --gray-light: #F8F9FA;
    --gray-medium: #6C757D;
    --gray-dark: #343A40;
    --gray-gradient: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
    
    /* Enhanced Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.18);
    --shadow-green: 0 4px 15px rgba(46, 125, 50, 0.25);
    
    /* Enhanced Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    
    /* Enhanced Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Enhanced Typography */
    --font-family-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
}

/* Global Enhanced Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-primary);
    line-height: 1.6;
    color: var(--gray-dark);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Enhanced Login Page Styles */
.login-body {
    background: var(--ndmc-green-gradient);
    min-height: 100vh;
    position: relative;
    overflow: hidden;
}

.login-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    pointer-events: none;
}

.login-branding {
    background: rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 3rem;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 450px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transform: translateY(0);
    transition: var(--transition-normal);
}

.login-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.login-container h2 {
    color: var(--ndmc-green);
    font-weight: var(--font-weight-bold);
    text-shadow: 0 2px 4px rgba(46, 125, 50, 0.2);
}

/* Enhanced Button Styles */
.btn {
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    padding: 0.75rem 1.5rem;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-normal);
}

.btn:hover::before {
    left: 100%;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
    box-shadow: var(--shadow-green);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--ndmc-green-dark) 0%, var(--ndmc-green) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.4);
}

.btn-primary {
    background: var(--ndmc-green-gradient);
    color: white;
    box-shadow: var(--shadow-green);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--ndmc-green-dark) 0%, var(--ndmc-green) 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.4);
}

.btn-warning {
    background: var(--warning-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
    border: none;
    font-weight: var(--font-weight-medium);
}

.btn-warning:hover {
    background: linear-gradient(135deg, #FF8F00 0%, #FFC107 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

.btn-info {
    background: var(--info-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
    border: none;
}

.btn-info:hover {
    background: linear-gradient(135deg, #1976D2 0%, #2196F3 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
    box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #D32F2F 0%, #F44336 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(244, 67, 54, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6C757D 0%, #868E96 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    border: none;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #495057 0%, #6C757D 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
}

.btn-outline-success {
    background: transparent;
    color: var(--ndmc-green);
    border: 2px solid var(--ndmc-green);
    position: relative;
    overflow: hidden;
}

.btn-outline-success::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--ndmc-green-gradient);
    transition: var(--transition-normal);
    z-index: -1;
}

.btn-outline-success:hover::before {
    left: 0;
}

.btn-outline-success:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-green);
}

.btn-outline-warning {
    background: transparent;
    color: #FFC107;
    border: 2px solid #FFC107;
    position: relative;
    overflow: hidden;
}

.btn-outline-warning::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--warning-gradient);
    transition: var(--transition-normal);
    z-index: -1;
}

.btn-outline-warning:hover::before {
    left: 0;
}

.btn-outline-warning:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

/* Override Bootstrap default button styles */
.btn[class*="btn-"] {
    background-color: initial;
    border-color: initial;
    color: initial;
}

.btn[class*="btn-"]:hover {
    background-color: initial;
    border-color: initial;
    color: initial;
}

.btn[class*="btn-"]:focus {
    background-color: initial;
    border-color: initial;
    color: initial;
    box-shadow: initial;
}

.btn[class*="btn-"]:active {
    background-color: initial;
    border-color: initial;
    color: initial;
}

/* Enhanced Form Controls */
.form-control,
.form-select {
    border-radius: var(--radius-md);
    border: 2px solid #E9ECEF;
    padding: 0.875rem 1.25rem;
    transition: var(--transition-normal);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    font-size: 0.95rem;
}

.form-control:focus,
.form-select:focus {
    border-color: var(--ndmc-green);
    box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.15);
    background: white;
    transform: translateY(-1px);
}

/* Enhanced Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Enhanced Sidebar Styles */
.sidebar {
    width: 280px;
    background: var(--ndmc-green-gradient);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: var(--transition-normal);
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="sidebar-grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23sidebar-grain)"/></svg>');
    pointer-events: none;
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.sidebar-logo {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    box-shadow: var(--shadow-md);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: var(--transition-normal);
}

.sidebar-logo:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.sidebar-brand {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    line-height: 1.3;
}

/* Enhanced User Info */
.user-info {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.15);
    margin: 1rem;
    border-radius: var(--radius-lg);
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-md);
}

.user-avatar {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.25);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    box-shadow: var(--shadow-sm);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: var(--transition-normal);
}

.user-avatar:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 255, 255, 0.3);
}

.user-role {
    font-size: 0.95rem;
    font-weight: var(--font-weight-semibold);
    margin-bottom: 0.25rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.user-name {
    font-size: 0.85rem;
    opacity: 0.9;
    font-weight: var(--font-weight-medium);
}

/* Enhanced Navigation Menu */
.nav-menu {
    padding: 1.5rem 0;
}

.nav-item {
    margin: 0.5rem 1rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    color: rgba(255, 255, 255, 0.85);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    font-size: 0.95rem;
    font-weight: var(--font-weight-medium);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-normal);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateX(8px);
    box-shadow: var(--shadow-md);
}

.nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    color: white;
    font-weight: var(--font-weight-semibold);
    box-shadow: var(--shadow-md);
    border-left: 4px solid white;
}

.nav-icon {
    width: 24px;
    margin-right: 14px;
    text-align: center;
    font-size: 1.1rem;
    transition: var(--transition-normal);
}

.nav-link:hover .nav-icon {
    transform: scale(1.1);
}

.nav-text {
    flex: 1;
    font-weight: var(--font-weight-medium);
}

/* Enhanced Main Content Area */
.main-content {
    flex: 1;
    margin-left: 280px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    transition: var(--transition-normal);
}

.main-content.expanded {
    margin-left: 70px;
}

/* Enhanced Top Navigation */
.top-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    padding: 1.25rem 2rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-md);
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    position: relative;
    overflow: visible;
    z-index: 1000;
}

.sidebar-toggle {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 1.2rem;
    color: var(--gray-medium);
    cursor: pointer;
    padding: 0.75rem;
    border-radius: var(--radius-md);
    transition: var(--transition-normal);
    backdrop-filter: blur(10px);
}

.sidebar-toggle:hover {
    background: var(--ndmc-green-gradient);
    color: white;
    transform: scale(1.05);
    box-shadow: var(--shadow-green);
}

.page-title {
    font-size: 1.75rem;
    font-weight: var(--font-weight-bold);
    color: var(--gray-dark);
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.top-nav-actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    position: relative;
    z-index: 1001;
}

.notification-badge {
    position: relative;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    padding: 0.75rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: var(--transition-normal);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    overflow: visible;
}

.notification-badge:hover {
    background: var(--ndmc-green-gradient);
    color: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-green);
}

.notification-badge i {
    font-size: 1.1rem;
    color: var(--gray-medium);
    transition: var(--transition-normal);
}

.notification-badge:hover i {
    color: white;
}

/* Notification badge count - if needed in future */
.notification-badge .badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #DC3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: var(--shadow-sm);
    z-index: 10;
}

/* Enhanced Dropdown Styles */
.dropdown {
    position: relative;
    z-index: 1002;
}

.dropdown-toggle {
    background: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    color: var(--gray-dark) !important;
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
    border-radius: var(--radius-md);
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 120px;
    justify-content: space-between;
    cursor: pointer;
}

.dropdown-toggle:hover {
    background: var(--ndmc-green-gradient) !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: var(--shadow-green);
}

.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.25rem rgba(46, 125, 50, 0.15) !important;
    border-color: var(--ndmc-green) !important;
    outline: none;
}

.dropdown-menu {
    border-radius: var(--radius-md);
    border: none;
    box-shadow: var(--shadow-lg);
    padding: 0.75rem 0;
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: absolute;
    top: 100%;
    right: 0;
    min-width: 200px;
    z-index: 1003;
    margin-top: 0.5rem;
    display: none;
    list-style: none;
    margin: 0;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
}

.dropdown-menu.show {
    display: block !important;
    z-index: 1003 !important;
    opacity: 1;
    transform: translateY(0);
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition-fast);
    border-radius: 0;
    color: var(--gray-dark);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    border: none;
    background: transparent;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.dropdown-item:hover {
    background: var(--ndmc-green-gradient);
    color: white;
    transform: translateX(5px);
    text-decoration: none;
}

.dropdown-item:focus {
    outline: none;
    background: var(--ndmc-green-gradient);
    color: white;
}

.dropdown-item i {
    width: 16px;
    text-align: center;
    font-size: 0.9rem;
}

.dropdown-divider {
    margin: 0.5rem 0;
    border-color: rgba(0, 0, 0, 0.1);
    height: 1px;
    background-color: rgba(0, 0, 0, 0.1);
    border: none;
}

/* Ensure dropdown appears above other elements */
.dropdown.show .dropdown-menu {
    display: block !important;
    z-index: 1003;
}

/* Fix for Bootstrap dropdown positioning */
.dropdown-menu.show {
    display: block !important;
    z-index: 1003 !important;
}

/* User dropdown specific styling */
.top-nav-actions .dropdown {
    position: relative;
}

.top-nav-actions .dropdown-menu {
    right: 0;
    left: auto;
    min-width: 180px;
}

/* Bootstrap dropdown override */
.dropdown-menu[data-bs-popper] {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    margin-top: 0.5rem !important;
}

/* Ensure dropdown items are properly styled */
.dropdown-menu li {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown-menu a {
    text-decoration: none;
    color: inherit;
}

/* Fix for dropdown toggle arrow */
.dropdown-toggle::after {
    margin-left: 0.5rem;
    transition: transform 0.2s ease;
}

.dropdown.show .dropdown-toggle::after {
    transform: rotate(180deg);
}

/* Enhanced Alerts */
.alert {
    border-radius: var(--radius-md);
    border: none;
    padding: 1.25rem 1.5rem;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    border-left: 4px solid;
}

/* Enhanced Dropdowns */
.dropdown-menu {
    border-radius: var(--radius-md);
    border: none;
    box-shadow: var(--shadow-lg);
    padding: 0.75rem 0;
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition-fast);
    border-radius: 0;
}

.dropdown-item:hover {
    background: var(--ndmc-green-gradient);
    color: white;
    transform: translateX(5px);
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 280px;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .login-container {
        padding: 2rem;
        margin: 1rem;
    }
    
    .content-area {
        padding: 1.5rem;
    }
    
    .top-nav {
        padding: 1rem;
    }
}

/* Enhanced Loading States */
.loading {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(46, 125, 50, 0.3);
    border-top: 2px solid var(--ndmc-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Modal Styles */
.modal-content {
    border-radius: var(--radius-lg);
    border: none;
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 2rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 2rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

/* Enhanced Print Styles */
@media print {
    .sidebar,
    .top-nav,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .content-area {
        padding: 0 !important;
    }
} 

/* Enhanced Table Action Buttons */
.btn-group-sm .btn-outline-primary {
    color: var(--ndmc-green);
    border-color: var(--ndmc-green);
    background: transparent;
    transition: var(--transition-normal);
}

.btn-group-sm .btn-outline-primary:hover {
    background: var(--ndmc-green);
    border-color: var(--ndmc-green);
    color: white;
    transform: scale(1.05);
    box-shadow: var(--shadow-green);
}

.btn-group-sm .btn-outline-secondary {
    color: var(--ndmc-green);
    border-color: var(--ndmc-green);
    background: transparent;
    transition: var(--transition-normal);
}

.btn-group-sm .btn-outline-secondary:hover {
    background: var(--ndmc-green);
    border-color: var(--ndmc-green);
    color: white;
    transform: scale(1.05);
    box-shadow: var(--shadow-green);
}

.btn-group-sm .btn-outline-danger {
    color: #DC3545;
    border-color: #DC3545;
    background: transparent;
    transition: var(--transition-normal);
}

.btn-group-sm .btn-outline-danger:hover {
    background: #DC3545;
    border-color: #DC3545;
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
}

.btn-group-sm .btn-outline-success {
    color: #198754;
    border-color: #198754;
    background: transparent;
    transition: var(--transition-normal);
}

.btn-group-sm .btn-outline-success:hover {
    background: #198754;
    border-color: #198754;
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3);
}

.btn-group-sm .btn-outline-info {
    color: #0DCAF0;
    border-color: #0DCAF0;
    background: transparent;
    transition: var(--transition-normal);
}

.btn-group-sm .btn-outline-info:hover {
    background: #0DCAF0;
    border-color: #0DCAF0;
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(13, 202, 240, 0.3);
}

/* Enhanced button group styling */
.btn-group-sm {
    gap: 2px;
}

.btn-group-sm .btn {
    border-radius: var(--radius-sm);
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.2;
    min-width: 32px;
    height: 32px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn-group-sm .btn i {
    font-size: 0.75rem;
}

/* Table action buttons container */
.table .btn-group-sm {
    display: flex;
    gap: 4px;
    flex-wrap: nowrap;
}

/* Specific button types for different actions */
.btn-view {
    color: var(--ndmc-green) !important;
    border-color: var(--ndmc-green) !important;
}

.btn-view:hover {
    background: var(--ndmc-green) !important;
    color: white !important;
    box-shadow: var(--shadow-green) !important;
}

.btn-edit {
    color: var(--ndmc-green) !important;
    border-color: var(--ndmc-green) !important;
}

.btn-edit:hover {
    background: var(--ndmc-green) !important;
    color: white !important;
    box-shadow: var(--shadow-green) !important;
}

.btn-delete {
    color: #DC3545 !important;
    border-color: #DC3545 !important;
}

.btn-delete:hover {
    background: #DC3545 !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3) !important;
}

.btn-approve {
    color: #198754 !important;
    border-color: #198754 !important;
}

.btn-approve:hover {
    background: #198754 !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3) !important;
}

.btn-reject {
    color: #DC3545 !important;
    border-color: #DC3545 !important;
}

.btn-reject:hover {
    background: #DC3545 !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3) !important;
}

.btn-comment {
    color: #0DCAF0 !important;
    border-color: #0DCAF0 !important;
}

.btn-comment:hover {
    background: #0DCAF0 !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(13, 202, 240, 0.3) !important;
}

.btn-recommend {
    color: #198754 !important;
    border-color: #198754 !important;
}

.btn-recommend:hover {
    background: #198754 !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(25, 135, 84, 0.3) !important;
} 

/* Enhanced Content Area */
.content-area {
    padding: 2.5rem;
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--ndmc-green-gradient);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-header {
    background: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.5rem 2rem;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    font-weight: var(--font-weight-semibold);
    color: var(--gray-dark);
    backdrop-filter: blur(10px);
}

.card-body {
    padding: 2rem;
}

/* Enhanced Status Indicators */
.status-approved {
    background: var(--success-gradient);
    color: white;
    border: none;
    box-shadow: var(--shadow-sm);
}

.status-pending {
    background: var(--warning-gradient);
    color: white;
    border: none;
    box-shadow: var(--shadow-sm);
}

.status-rejected {
    background: var(--danger-gradient);
    color: white;
    border: none;
    box-shadow: var(--shadow-sm);
}

.status-draft {
    background: var(--info-gradient);
    color: white;
    border: none;
    box-shadow: var(--shadow-sm);
}

/* Enhanced Tables */
.table {
    width: 100%;
    margin-bottom: 1.5rem;
    background: transparent;
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background: rgba(248, 249, 250, 0.9);
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    padding: 1.25rem 1rem;
    font-weight: var(--font-weight-semibold);
    text-align: left;
    vertical-align: top;
    color: var(--gray-dark);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

/* Center the Actions column (5th column) */
.table thead th:nth-child(5),
.table tbody td:nth-child(5) {
    text-align: center;
}

.table tbody td {
    vertical-align: middle;
    padding: 1.25rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    word-wrap: break-word;
    min-width: 0;
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background: rgba(46, 125, 50, 0.05);
    transform: scale(1.01);
}

.table tbody tr:hover td {
    box-shadow: 0 2px 8px rgba(46, 125, 50, 0.1);
}

/* Enhanced Badges */
.badge {
    border-radius: var(--radius-sm);
    font-weight: var(--font-weight-medium);
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    line-height: 1.2;
    white-space: nowrap;
    overflow: visible;
    text-overflow: clip;
    min-width: fit-content;
    display: inline-block;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.badge:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

/* Enhanced Progress Bars */
.progress {
    height: 12px;
    border-radius: var(--radius-sm);
    background: rgba(0, 0, 0, 0.1);
    overflow: hidden;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    background: var(--ndmc-green-gradient);
    border-radius: var(--radius-sm);
    transition: var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
} 