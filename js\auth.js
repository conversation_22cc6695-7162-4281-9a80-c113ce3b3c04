// ADAMS Authentication System
class ADAMSAuth {
    constructor() {
        this.credentials = {
            admin: { username: 'admin', password: 'admin123', role: 'admin', name: 'System Administrator' },
            areachair: { username: 'areachair', password: 'chair123', role: 'areachair', name: 'Area Chair' },
            subareachair: { username: 'subareachair', password: 'subchair123', role: 'subareachair', name: 'Sub-Area Chair' },
            member: { username: 'member', password: 'member123', role: 'member', name: 'Member' }
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        // Temporarily disable auth status check for testing
        // this.checkAuthStatus();
    }
    
    setupEventListeners() {
        // Login form submission
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }
        
        // Password toggle
        const togglePassword = document.getElementById('togglePassword');
        if (togglePassword) {
            togglePassword.addEventListener('click', () => this.togglePasswordVisibility());
        }
        
        // Role selection change
        const roleSelect = document.getElementById('role');
        if (roleSelect) {
            roleSelect.addEventListener('change', () => this.updateCredentials());
        }
    }
    
    handleLogin(e) {
        e.preventDefault();
        
        const form = e.target;
        const role = document.getElementById('role').value;
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        // Clear previous errors
        this.clearErrors();
        
        // Validate form
        if (!this.validateForm(role, username, password)) {
            return;
        }
        
        // Authenticate user
        const user = this.authenticateUser(role, username, password);
        
        if (user) {
            this.loginSuccess(user);
        } else {
            this.showError('Invalid credentials. Please check your username and password.');
        }
    }
    
    validateForm(role, username, password) {
        let isValid = true;
        
        if (!role) {
            this.showFieldError('role', 'Please select your role.');
            isValid = false;
        }
        
        if (!username.trim()) {
            this.showFieldError('username', 'Please enter your username.');
            isValid = false;
        }
        
        if (!password) {
            this.showFieldError('password', 'Please enter your password.');
            isValid = false;
        }
        
        return isValid;
    }
    
    authenticateUser(role, username, password) {
        const expectedCreds = this.credentials[role];
        
        if (!expectedCreds) {
            return null;
        }
        
        if (username === expectedCreds.username && password === expectedCreds.password) {
            return {
                role: expectedCreds.role,
                username: expectedCreds.username,
                name: expectedCreds.name,
                loginTime: new Date().toISOString()
            };
        }
        
        return null;
    }
    
    loginSuccess(user) {
        // Store user session
        localStorage.setItem('adams_user', JSON.stringify(user));
        localStorage.setItem('adams_login_time', new Date().toISOString());
        
        // Show success message
        this.showSuccess('Login successful! Redirecting...');
        
        // Redirect to appropriate dashboard
        setTimeout(() => {
            this.redirectToDashboard(user.role);
        }, 1000);
    }
    
    redirectToDashboard(role) {
        const dashboards = {
            admin: 'dashboards/admin.html',
            areachair: 'dashboards/areachair.html',
            subareachair: 'dashboards/subareachair.html',
            member: 'dashboards/member.html'
        };

        const dashboardUrl = dashboards[role];
        if (dashboardUrl) {
            window.location.href = dashboardUrl;
        }
    }
    
    updateCredentials() {
        const role = document.getElementById('role').value;
        const usernameField = document.getElementById('username');
        const passwordField = document.getElementById('password');
        
        if (role && this.credentials[role]) {
            usernameField.value = this.credentials[role].username;
            passwordField.value = this.credentials[role].password;
        } else {
            usernameField.value = '';
            passwordField.value = '';
        }
    }
    
    togglePasswordVisibility() {
        const passwordField = document.getElementById('password');
        const toggleBtn = document.getElementById('togglePassword');
        const icon = toggleBtn.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }
    
    showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        
        field.classList.add('is-invalid');
        if (feedback) {
            feedback.textContent = message;
        }
    }
    
    showError(message) {
        const errorDiv = document.getElementById('loginError');
        const errorMessage = document.getElementById('errorMessage');
        
        if (errorDiv && errorMessage) {
            errorMessage.textContent = message;
            errorDiv.classList.remove('d-none');
        }
    }
    
    showSuccess(message) {
        // Create success alert
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const form = document.getElementById('loginForm');
        form.insertBefore(alertDiv, form.firstChild);
    }
    
    clearErrors() {
        // Clear field errors
        const fields = document.querySelectorAll('.is-invalid');
        fields.forEach(field => {
            field.classList.remove('is-invalid');
        });
        
        // Clear general error
        const errorDiv = document.getElementById('loginError');
        if (errorDiv) {
            errorDiv.classList.add('d-none');
        }
    }
    
    checkAuthStatus() {
        // Don't redirect if we're already on a dashboard page
        if (window.location.pathname.includes('/dashboards/')) {
            return;
        }
        
        const user = localStorage.getItem('adams_user');
        if (user) {
            try {
                const userData = JSON.parse(user);
                this.redirectToDashboard(userData.role);
            } catch (e) {
                localStorage.removeItem('adams_user');
            }
        }
    }
    
    static logout() {
        localStorage.removeItem('adams_user');
        localStorage.removeItem('adams_login_time');
        window.location.href = '/index.html';
    }
    
    static getCurrentUser() {
        const user = localStorage.getItem('adams_user');
        return user ? JSON.parse(user) : null;
    }
    
    static isAuthenticated() {
        return !!localStorage.getItem('adams_user');
    }
    
    static checkAuth() {
        if (!ADAMSAuth.isAuthenticated()) {
            window.location.href = '/index.html';
            return false;
        }
        return true;
    }
}

// Initialize authentication system
document.addEventListener('DOMContentLoaded', () => {
    new ADAMSAuth();
});

// Global logout function
window.logout = ADAMSAuth.logout; 