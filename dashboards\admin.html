<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Accreditation Data Administration & Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../css/styles.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="sidebar-brand">Accreditation Data Administration & Management System</div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="users">
                        <i class="fas fa-users nav-icon"></i>
                        <span class="nav-text">User Management</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="config">
                        <i class="fas fa-cogs nav-icon"></i>
                        <span class="nav-text">System Configuration</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="scheduling">
                        <i class="fas fa-calendar-alt nav-icon"></i>
                        <span class="nav-text">Accreditation Scheduling</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="documents">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">Document Management</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="logs">
                        <i class="fas fa-history nav-icon"></i>
                        <span class="nav-text">Audit Logs</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="reports">
                        <i class="fas fa-chart-bar nav-icon"></i>
                        <span class="nav-text">Reports & Analytics</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="announcements">
                        <i class="fas fa-bullhorn nav-icon"></i>
                        <span class="nav-text">Announcements</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="settings">
                        <i class="fas fa-cog nav-icon"></i>
                        <span class="nav-text">Settings</span>
                    </a>
                </div>
                
                <hr class="my-3 mx-3" style="border-color: rgba(255,255,255,0.2);">
                
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="logout()">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <div class="d-flex align-items-center">
                    <button class="sidebar-toggle me-3" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="sidebar-toggle d-lg-none" id="mobileSidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title mb-0" id="pageTitle">Dashboard</h1>
                </div>
                
                <div class="top-nav-actions">
                    <div class="notification-badge me-3">
                        <i class="fas fa-bell fa-lg"></i>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>
                            <span id="topUserName">admin</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="content-area" id="contentArea">
                <!-- Dashboard content will be loaded here -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- <script src="../js/auth.js"></script> -->
    <!-- <script src="../js/utils.js"></script> -->
    <!-- <script src="../js/dashboard.js"></script> -->
    <script>
        // Admin-specific dashboard functionality
        class AdminDashboard {
            constructor() {
                this.currentUser = { username: 'admin', name: 'System Administrator', role: 'admin' };
                this.currentPage = 'dashboard';
                this.init();
            }
            
            init() {
                this.setupSidebar();
                this.setupNavigation();
                this.loadDashboardContent();
                this.setupEventListeners();
            }
            
            setupSidebar() {
                const sidebar = document.getElementById('sidebar');
                const sidebarToggle = document.getElementById('sidebarToggle');
                const mainContent = document.querySelector('.main-content');
                
                if (sidebarToggle) {
                    sidebarToggle.addEventListener('click', () => {
                        sidebar.classList.toggle('collapsed');
                        mainContent.classList.toggle('expanded');
                    });
                }
            }
            
            setupNavigation() {
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const page = link.getAttribute('data-page');
                        if (page) {
                            this.navigateToPage(page);
                        }
                    });
                });
            }
            
            navigateToPage(page) {
                this.currentPage = page;
                this.setActiveNavLink();
                this.loadPageContent(page);
                this.updatePageTitle(page);
            }
            
            setActiveNavLink() {
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('data-page') === this.currentPage) {
                        link.classList.add('active');
                    }
                });
            }
            
            updatePageTitle(page) {
                const pageTitle = document.getElementById('pageTitle');
                if (pageTitle) {
                    const titles = {
                        dashboard: 'Dashboard',
                        users: 'User Management',
                        config: 'System Configuration',
                        scheduling: 'Accreditation Scheduling',
                        documents: 'Document Management',
                        logs: 'Audit Logs',
                        reports: 'Reports & Analytics',
                        announcements: 'Announcements',
                        settings: 'Settings'
                    };
                    pageTitle.textContent = titles[page] || 'Dashboard';
                }
            }
            
            loadPageContent(page) {
                const contentArea = document.getElementById('contentArea');
                if (!contentArea) return;
                
                switch (page) {
                    case 'dashboard':
                        this.renderDashboard(contentArea);
                        break;
                    case 'users':
                        this.renderUserManagement(contentArea);
                        break;
                    case 'config':
                        this.renderSystemConfig(contentArea);
                        break;
                    case 'scheduling':
                        this.renderScheduling(contentArea);
                        break;
                    case 'documents':
                        this.renderDocuments(contentArea);
                        break;
                    case 'logs':
                        this.renderAuditLogs(contentArea);
                        break;
                    case 'reports':
                        this.renderReports(contentArea);
                        break;
                    case 'announcements':
                        this.renderAnnouncements(contentArea);
                        break;
                    case 'settings':
                        this.renderSettings(contentArea);
                        break;
                    default:
                        this.renderDashboard(contentArea);
                }
            }
            
            loadDashboardContent() {
                this.renderDashboard(document.getElementById('contentArea'));
            }
            
            setupEventListeners() {
                // Global event listeners
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.btn-delete')) {
                        this.handleDelete(e);
                    }
                    if (e.target.closest('.btn-edit')) {
                        this.handleEdit(e);
                    }
                    if (e.target.closest('.btn-view')) {
                        this.handleView(e);
                    }
                });
            }
            
            handleDelete(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to delete this item?')) {
                    alert('Item deleted successfully');
                }
            }
            
            handleEdit(e) {
                e.preventDefault();
                alert('Edit functionality would be implemented here');
            }
            
            handleView(e) {
                e.preventDefault();
                alert('View functionality would be implemented here');
            }
            
            renderDashboard(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Admin Dashboard</h2>
                            <p class="text-muted">Welcome back, ${this.currentUser.name}!</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">150</h4>
                                            <p class="mb-0">Total Users</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-users fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">85%</h4>
                                            <p class="mb-0">Completion Rate</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-chart-line fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">12</h4>
                                            <p class="mb-0">Pending Reviews</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">45</h4>
                                            <p class="mb-0">Documents</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-file-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Recent Activity</h5>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">New user registered</h6>
                                                <small class="text-muted">John Doe joined the system</small>
                                            </div>
                                            <small class="text-muted">2 hours ago</small>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Document uploaded</h6>
                                                <small class="text-muted">Faculty credentials submitted</small>
                                            </div>
                                            <small class="text-muted">4 hours ago</small>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Review completed</h6>
                                                <small class="text-muted">Research proposal approved</small>
                                            </div>
                                            <small class="text-muted">1 day ago</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>Add User
                                        </button>
                                        <button class="btn btn-success">
                                            <i class="fas fa-file-upload me-2"></i>Upload Document
                                        </button>
                                        <button class="btn btn-info">
                                            <i class="fas fa-chart-bar me-2"></i>Generate Report
                                        </button>
                                        <button class="btn btn-warning">
                                            <i class="fas fa-bell me-2"></i>Send Announcement
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderUserManagement(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <h2>User Management</h2>
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>Add User
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">System Users</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Email</th>
                                            <th>Role</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>John Doe</td>
                                            <td><EMAIL></td>
                                            <td><span class="badge bg-primary">Admin</span></td>
                                            <td><span class="badge bg-success">Active</span></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-view" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-edit" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-delete" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Jane Smith</td>
                                            <td><EMAIL></td>
                                            <td><span class="badge bg-info">Area Chair</span></td>
                                            <td><span class="badge bg-success">Active</span></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-view" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-edit" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-delete" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderSystemConfig(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>System Configuration</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Accreditation Agencies</h5>
                        </div>
                        <div class="card-body">
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Commission on Higher Education (CHED)
                                    <span class="badge bg-primary rounded-pill">Active</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Philippine Association of Colleges and Universities (PACU)
                                    <span class="badge bg-primary rounded-pill">Active</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                `;
            }
            
            renderScheduling(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Accreditation Scheduling</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Timeline</h5>
                        </div>
                        <div class="card-body">
                            <div class="timeline">
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-success"></div>
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Phase 1: Preparation</h6>
                                            <p class="text-muted mb-0">Document collection and organization</p>
                                            <small class="text-muted">Completed</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-marker bg-warning"></div>
                                    <div class="card">
                                        <div class="card-body">
                                            <h6>Phase 2: Review</h6>
                                            <p class="text-muted mb-0">Internal review and validation</p>
                                            <small class="text-muted">In Progress</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderDocuments(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Document Management</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">System Documents</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Document</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Uploaded</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Faculty Credentials</td>
                                            <td>Faculty</td>
                                            <td><span class="badge bg-success">Approved</span></td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-view" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-edit" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderAuditLogs(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Audit Logs</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Recent Activity</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Action</th>
                                            <th>Timestamp</th>
                                            <th>IP Address</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>admin</td>
                                            <td>User login</td>
                                            <td>2024-01-15 10:30:00</td>
                                            <td>*************</td>
                                        </tr>
                                        <tr>
                                            <td>areachair</td>
                                            <td>Document review</td>
                                            <td>2024-01-15 09:15:00</td>
                                            <td>*************</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderReports(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Reports & Analytics</h2>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Document Status</h5>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">Chart placeholder for document status</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Activity Overview</h5>
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">Chart placeholder for activity overview</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Generate Reports</h5>
                                </div>
                                <div class="card-body">
                                    <div class="btn-group">
                                        <button class="btn btn-primary">PDF Report</button>
                                        <button class="btn btn-success">Excel Export</button>
                                        <button class="btn btn-info">Analytics Report</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderAnnouncements(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center">
                                <h2>Announcements</h2>
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>New Announcement
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">System Announcements</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">System Maintenance</h6>
                                            <p class="mb-1">Scheduled maintenance on January 20, 2024</p>
                                            <small class="text-muted">Posted by admin on Jan 15, 2024</small>
                                        </div>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-secondary btn-edit" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger btn-delete" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderSettings(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Settings</h2>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Security Settings</h5>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="mb-3">
                                            <label class="form-label">Session Timeout</label>
                                            <select class="form-select">
                                                <option>30 minutes</option>
                                                <option>1 hour</option>
                                                <option>2 hours</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Password Policy</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" checked>
                                                <label class="form-check-label">Require strong passwords</label>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Save Settings</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-6 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Notification Settings</h5>
                                </div>
                                <div class="card-body">
                                    <form>
                                        <div class="mb-3">
                                            <label class="form-label">Email Notifications</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" checked>
                                                <label class="form-check-label">Document submissions</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" checked>
                                                <label class="form-check-label">Review requests</label>
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-primary">Save Settings</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }
        
        // Initialize admin dashboard
        document.addEventListener('DOMContentLoaded', () => {
            new AdminDashboard();
        });
        
        // Global logout function
        function logout() {
            window.location.href = '/index.html';
        }
    </script>
    
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 30px;
        }
        
        .timeline-marker {
            position: absolute;
            left: -35px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #dee2e6;
        }
        
        .timeline-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: -29px;
            top: 12px;
            width: 2px;
            height: calc(100% + 18px);
            background: #dee2e6;
        }
        
        .deadline-item {
            padding: 10px 0;
        }
        
        .announcement-item:last-child {
            border-bottom: none !important;
        }
    </style>
</body>
</html> 