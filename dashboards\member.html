<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Dashboard - Accreditation Data Administration & Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="../css/styles.css" rel="stylesheet">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="sidebar-brand">Accreditation Data Administration & Management System</div>
            </div>
            
            <nav class="nav-menu">
                <div class="nav-item">
                    <a href="#" class="nav-link active" data-page="dashboard">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="upload">
                        <i class="fas fa-upload nav-icon"></i>
                        <span class="nav-text">Document Upload</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="documents">
                        <i class="fas fa-file-alt nav-icon"></i>
                        <span class="nav-text">Document Management</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="tracking">
                        <i class="fas fa-search nav-icon"></i>
                        <span class="nav-text">Status Tracking</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="requests">
                        <i class="fas fa-key nav-icon"></i>
                        <span class="nav-text">Access Requests</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="#" class="nav-link" data-page="calendar">
                        <i class="fas fa-calendar nav-icon"></i>
                        <span class="nav-text">Deadline Calendar</span>
                    </a>
                </div>
                <hr class="my-3 mx-3" style="border-color: rgba(255,255,255,0.2);">
                <div class="nav-item">
                    <a href="#" class="nav-link" onclick="logout()">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>
            </nav>
        </div>
        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Navigation -->
            <div class="top-nav">
                <div class="d-flex align-items-center">
                    <button class="sidebar-toggle me-3" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <button class="sidebar-toggle d-lg-none" id="mobileSidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="page-title mb-0" id="pageTitle">Dashboard</h1>
                </div>
                <div class="top-nav-actions">
                    <div class="notification-badge me-3">
                        <i class="fas fa-bell fa-lg"></i>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>
                            <span id="topUserName">member</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>Settings</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <!-- Content Area -->
            <div class="content-area" id="contentArea">
                <!-- Dashboard content will be loaded here -->
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- <script src="../js/auth.js"></script> -->
    <!-- <script src="../js/utils.js"></script> -->
    <!-- <script src="../js/dashboard.js"></script> -->
    <script>
        // Member-specific dashboard functionality
        class MemberDashboard {
            constructor() {
                this.currentUser = { username: 'member', name: 'Member', role: 'member' };
                this.currentPage = 'dashboard';
                this.init();
            }
            
            init() {
                this.setupSidebar();
                this.setupNavigation();
                this.loadDashboardContent();
                this.setupEventListeners();
            }
            
            setupSidebar() {
                const sidebar = document.getElementById('sidebar');
                const sidebarToggle = document.getElementById('sidebarToggle');
                const mainContent = document.querySelector('.main-content');
                
                if (sidebarToggle) {
                    sidebarToggle.addEventListener('click', () => {
                        sidebar.classList.toggle('collapsed');
                        mainContent.classList.toggle('expanded');
                    });
                }
            }
            
            setupNavigation() {
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', (e) => {
                        e.preventDefault();
                        const page = link.getAttribute('data-page');
                        if (page) {
                            this.navigateToPage(page);
                        }
                    });
                });
            }
            
            navigateToPage(page) {
                this.currentPage = page;
                this.setActiveNavLink();
                this.loadPageContent(page);
                this.updatePageTitle(page);
            }
            
            setActiveNavLink() {
                const navLinks = document.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('data-page') === this.currentPage) {
                        link.classList.add('active');
                    }
                });
            }
            
            updatePageTitle(page) {
                const pageTitle = document.getElementById('pageTitle');
                if (pageTitle) {
                    const titles = {
                        dashboard: 'Dashboard',
                        upload: 'Document Upload',
                        documents: 'Document Management',
                        tracking: 'Status Tracking',
                        requests: 'Access Requests',
                        calendar: 'Deadline Calendar'
                    };
                    pageTitle.textContent = titles[page] || 'Dashboard';
                }
            }
            
            loadPageContent(page) {
                const contentArea = document.getElementById('contentArea');
                if (!contentArea) return;
                
                switch (page) {
                    case 'dashboard':
                        this.renderDashboard(contentArea);
                        break;
                    case 'upload':
                        this.renderDocumentUpload(contentArea);
                        break;
                    case 'documents':
                        this.renderDocumentManagement(contentArea);
                        break;
                    case 'tracking':
                        this.renderStatusTracking(contentArea);
                        break;
                    case 'requests':
                        this.renderAccessRequests(contentArea);
                        break;
                    case 'calendar':
                        this.renderCalendar(contentArea);
                        break;
                    default:
                        this.renderDashboard(contentArea);
                }
            }
            
            loadDashboardContent() {
                this.renderDashboard(document.getElementById('contentArea'));
            }
            
            setupEventListeners() {
                // Global event listeners
                document.addEventListener('click', (e) => {
                    if (e.target.closest('.btn-delete')) {
                        this.handleDelete(e);
                    }
                    if (e.target.closest('.btn-edit')) {
                        this.handleEdit(e);
                    }
                    if (e.target.closest('.btn-view')) {
                        this.handleView(e);
                    }
                });
            }
            
            handleDelete(e) {
                e.preventDefault();
                if (confirm('Are you sure you want to delete this item?')) {
                    alert('Item deleted successfully');
                }
            }
            
            handleEdit(e) {
                e.preventDefault();
                alert('Edit functionality would be implemented here');
            }
            
            handleView(e) {
                e.preventDefault();
                alert('View functionality would be implemented here');
            }
            
            renderDashboard(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Member Dashboard</h2>
                            <p class="text-muted">Welcome back, ${this.currentUser.name}!</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">5</h4>
                                            <p class="mb-0">My Documents</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-file-alt fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">3</h4>
                                            <p class="mb-0">Approved</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">1</h4>
                                            <p class="mb-0">Pending</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-3 col-md-6 mb-4">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 class="mb-0">2</h4>
                                            <p class="mb-0">Requests</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-key fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-lg-8 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Recent Activity</h5>
                                </div>
                                <div class="card-body">
                                    <div class="list-group list-group-flush">
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Document uploaded</h6>
                                                <small class="text-muted">Faculty credentials submitted</small>
                                            </div>
                                            <small class="text-muted">2 hours ago</small>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Document approved</h6>
                                                <small class="text-muted">Research proposal approved</small>
                                            </div>
                                            <small class="text-muted">1 day ago</small>
                                        </div>
                                        <div class="list-group-item d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-1">Access request submitted</h6>
                                                <small class="text-muted">Request for additional data</small>
                                            </div>
                                            <small class="text-muted">3 days ago</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-lg-4 mb-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">Quick Actions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button class="btn btn-primary">
                                            <i class="fas fa-upload me-2"></i>Upload Document
                                        </button>
                                        <button class="btn btn-success">
                                            <i class="fas fa-key me-2"></i>Request Access
                                        </button>
                                        <button class="btn btn-info">
                                            <i class="fas fa-search me-2"></i>Track Status
                                        </button>
                                        <button class="btn btn-warning">
                                            <i class="fas fa-calendar me-2"></i>View Calendar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderDocumentUpload(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Document Upload</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Upload Documents</h5>
                        </div>
                        <div class="card-body">
                            <div class="upload-area border-2 border-dashed border-secondary rounded p-5 text-center">
                                <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                <h5>Drag and drop files here</h5>
                                <p class="text-muted">or click to browse</p>
                                <button class="btn btn-primary">Choose Files</button>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderDocumentManagement(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Document Management</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">My Documents</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Document</th>
                                            <th>Type</th>
                                            <th>Status</th>
                                            <th>Uploaded</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Faculty Credentials</td>
                                            <td>Faculty</td>
                                            <td><span class="badge bg-success">Approved</span></td>
                                            <td>2 days ago</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-view" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-edit" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-delete" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Research Proposal</td>
                                            <td>Research</td>
                                            <td><span class="badge bg-warning">Pending</span></td>
                                            <td>1 day ago</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-view" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-edit" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-delete" title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderStatusTracking(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Status Tracking</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Submission Progress</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">Faculty Credentials</h6>
                                        <p class="text-muted mb-1">Submitted: 2 days ago</p>
                                        <p class="text-muted mb-1">Reviewer: Area Chair</p>
                                        <p class="text-muted mb-0">Comment: All requirements met</p>
                                    </div>
                                    <div><span class="badge bg-success">Approved</span></div>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">Research Proposal</h6>
                                        <p class="text-muted mb-1">Submitted: 1 day ago</p>
                                        <p class="text-muted mb-1">Reviewer: Sub-Area Chair</p>
                                        <p class="text-muted mb-0">Comment: Under review</p>
                                    </div>
                                    <div><span class="badge bg-warning">Pending</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderAccessRequests(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Access Requests</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">My Access Requests</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Document</th>
                                            <th>Reason</th>
                                            <th>Status</th>
                                            <th>Requested</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Research Data 2024</td>
                                            <td>Need access for analysis</td>
                                            <td><span class="badge bg-warning">Pending</span></td>
                                            <td>1 day ago</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-view" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger btn-delete" title="Cancel">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Faculty Records</td>
                                            <td>Required for accreditation</td>
                                            <td><span class="badge bg-success">Approved</span></td>
                                            <td>3 days ago</td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary btn-view" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            renderCalendar(container) {
                container.innerHTML = `
                    <div class="row mb-4">
                        <div class="col-12">
                            <h2>Deadline Calendar</h2>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Important Deadlines</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">Document Submission</h6>
                                        <p class="text-muted mb-0">2024-12-20</p>
                                    </div>
                                    <span class="badge bg-danger">submission</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">Review Deadline</h6>
                                        <p class="text-muted mb-0">2024-12-25</p>
                                    </div>
                                    <span class="badge bg-warning">review</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">Final Approval</h6>
                                        <p class="text-muted mb-0">2024-12-30</p>
                                    </div>
                                    <span class="badge bg-success">approval</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
        }
        
        // Initialize member dashboard
        document.addEventListener('DOMContentLoaded', () => {
            new MemberDashboard();
        });
        
        // Global logout function
        function logout() {
            window.location.href = '/index.html';
        }
    </script>
    <style>
        .upload-area {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            background-color: var(--gray-light);
        }
        .upload-area.border-primary {
            border-color: var(--ndmc-green) !important;
        }
    </style>
</body>
</html> 