<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page - ADAMS</title>
</head>
<body>
    <h1>ADAMS Test Page</h1>
    <p>Server is working!</p>
    <button onclick="setAuth()">Set Admin Auth</button>
    <button onclick="goToAdmin()">Go to Admin Dashboard</button>
    <button onclick="goToAreaChair()">Go to Area Chair Dashboard</button>
    <button onclick="goToSubAreaChair()">Go to Sub-Area Chair Dashboard</button>
    <button onclick="goToMember()">Go to Member Dashboard</button>
    
    <script>
        function setAuth() {
            const user = {
                role: 'admin',
                username: 'admin',
                name: 'System Administrator',
                loginTime: new Date().toISOString()
            };
            localStorage.setItem('adams_user', JSON.stringify(user));
            alert('Admin authentication set!');
        }
        
        function goToAdmin() {
            window.location.href = '/dashboards/admin.html';
        }
        
        function goToAreaChair() {
            window.location.href = '/dashboards/areachair.html';
        }
        
        function goToSubAreaChair() {
            window.location.href = '/dashboards/subareachair.html';
        }
        
        function goToMember() {
            window.location.href = '/dashboards/member.html';
        }
    </script>
</body>
</html> 